{"name": "console", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "taze": "taze --all"}, "dependencies": {"@iconify-json/logos": "^1.2.5", "@iconify-json/lucide": "^1.2.20", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.10", "@nuxt/ui": "3.3.0", "@sidebase/nuxt-auth": "^1.0.0", "@tanstack/vue-table": "^8.21.3", "@tanstack/vue-virtual": "^3.13.12", "@unhead/vue": "^2.0.12", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "next-auth": "4.21.1", "nuxt": "^4.0.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxt/eslint": "1.7.1", "@nuxt/test-utils": "3.19.2", "eslint": "^9.32.0", "sass-embedded": "^1.89.2", "tailwindcss": "^4.1.11", "taze": "^19.1.0", "typescript": "^5.8.3"}}