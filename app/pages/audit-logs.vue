<script setup lang="ts">
import { computed, watchEffect } from 'vue';
import { useVirtualizer } from '@tanstack/vue-virtual';

type Payment = {
    date: string;
    actor: string;
    action: string;
};

// Generate more data for infinite scroll demonstration
const generateMoreData = (count: number): Payment[] => {
    const actors = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>',
    ];

    const actions = [
        'created a new user', 'updated user information', 'deleted a user',
        'created a new role', 'updated role permissions', 'deleted a role',
        'assigned a role to a user', 'revoked a role from a user',
    ];

    return Array.from({ length: count }, () => ({
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        actor: actors[Math.floor(Math.random() * actors.length)] || '<EMAIL>',
        action: actions[Math.floor(Math.random() * actions.length)] || 'Unknown action',
    }));
};

const allData = ref<Payment[]>([
    ...generateMoreData(100), // Start with more initial data
]);

const displayedData = ref<Payment[]>(allData.value.slice(0, 50));
const isLoading = ref(false);
const hasMore = ref(true);

// Global filter state
const globalFilter = ref('');

// Expanded rows state
const expanded = ref<Record<string, boolean>>({});

// Infinite scroll logic
const loadMore = async () => {
    if (isLoading.value || !hasMore.value) return;

    isLoading.value = true;

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const currentLength = displayedData.value.length;
    const nextBatch = allData.value.slice(currentLength, currentLength + 20);

    if (nextBatch.length === 0) {
        // Generate more data if we've reached the end
        const newData = generateMoreData(50);
        allData.value.push(...newData);
        displayedData.value.push(...newData.slice(0, 20));
    }
    else {
        displayedData.value.push(...nextBatch);
    }

    if (displayedData.value.length >= 500) {
        hasMore.value = false;
    }

    isLoading.value = false;
};

// Virtual scrolling setup
const parentRef = ref<HTMLElement | null>(null);

const rowVirtualizerOptions = computed(() => {
    return {
        count: hasMore.value ? displayedData.value.length + 1 : displayedData.value.length,
        getScrollElement: () => parentRef.value,
        estimateSize: () => 60, // Estimated row height
        overscan: 5,
    };
});

const rowVirtualizer = useVirtualizer(rowVirtualizerOptions);

const virtualRows = computed(() => rowVirtualizer.value.getVirtualItems());

const totalSize = computed(() => rowVirtualizer.value.getTotalSize());

// Watch for when we need to load more data
watchEffect(() => {
    const [lastItem] = [...virtualRows.value].reverse();

    if (!lastItem) {
        return;
    }

    if (
        lastItem.index >= displayedData.value.length - 1
        && hasMore.value
        && !isLoading.value
    ) {
        loadMore();
    }
});
</script>

<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-6 flex-shrink-0">
            <div class="max-w-7xl mx-auto">
                <div class="flex items-center justify-between">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        Audit Logs (Virtualized)
                    </h1>

                    <!-- Global Filter Input -->
                    <UInput
                        v-model="globalFilter"
                        placeholder="Search all columns..."
                        icon="i-lucide-search"
                        size="md"
                    />
                </div>
            </div>
        </div>

        <div class="flex-1 px-6 overflow-hidden">
            <div class="max-w-7xl mx-auto h-[calc(100%-24px)]">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                    <!-- Table Header -->
                    <div class="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
                        <div class="grid grid-cols-12 gap-4 p-4 text-sm font-medium text-gray-900 dark:text-white">
                            <div class="col-span-1">
                                <UButton
                                    color="neutral"
                                    variant="ghost"
                                    icon="i-lucide-chevron-down"
                                    square
                                    size="xs"
                                    class="opacity-0"
                                />
                            </div>
                            <div class="col-span-3">
                                <UButton
                                    color="neutral"
                                    variant="ghost"
                                    label="Date"
                                    icon="i-lucide-arrow-up-down"
                                    class="cursor-pointer -mx-2.5 text-xs"
                                />
                            </div>
                            <div class="col-span-4">
                                <UButton
                                    color="neutral"
                                    variant="ghost"
                                    label="Actor"
                                    icon="i-lucide-arrow-up-down"
                                    class="cursor-pointer -mx-2.5 text-xs"
                                />
                            </div>
                            <div class="col-span-4">
                                <span class="text-xs">Action</span>
                            </div>
                        </div>
                    </div>

                    <!-- Virtual Scrolling Container -->
                    <div
                        ref="parentRef"
                        class="flex-1 overflow-auto"
                        style="height: 100%"
                    >
                        <div
                            :style="{
                                height: `${totalSize}px`,
                                width: '100%',
                                position: 'relative',
                            }"
                        >
                            <div
                                v-for="virtualRow in virtualRows"
                                :key="virtualRow.key"
                                :style="{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: `${virtualRow.size}px`,
                                    transform: `translateY(${virtualRow.start}px)`,
                                }"
                                class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900/50"
                            >
                                <template v-if="virtualRow.index >= displayedData.length">
                                    <!-- Loading row -->
                                    <div class="p-4 text-center">
                                        <div
                                            v-if="hasMore"
                                            class="flex items-center justify-center space-x-2"
                                        >
                                            <UIcon
                                                name="i-lucide-loader-circle"
                                                class="size-4 animate-spin text-primary"
                                            />
                                            <span class="text-sm text-gray-600 dark:text-gray-400">Loading more...</span>
                                        </div>
                                        <div
                                            v-else
                                            class="text-sm text-gray-600 dark:text-gray-400"
                                        >
                                            No more data to load
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <!-- Data row -->
                                    <div class="grid grid-cols-12 gap-4 p-4 items-center">
                                        <div class="col-span-1">
                                            <UButton
                                                color="neutral"
                                                variant="ghost"
                                                icon="i-lucide-chevron-down"
                                                square
                                                size="xs"
                                                class="cursor-pointer"
                                                :class="expanded[virtualRow.index] ? 'rotate-180' : ''"
                                                @click="expanded[virtualRow.index] = !expanded[virtualRow.index]"
                                            />
                                        </div>
                                        <div class="col-span-3 text-sm">
                                            {{ new Date(displayedData[virtualRow.index].date).toLocaleString('en-US', {
                                                day: 'numeric',
                                                month: 'short',
                                                year: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                                hour12: true,
                                            }) }}
                                        </div>
                                        <div class="col-span-4 text-sm">
                                            {{ displayedData[virtualRow.index].actor }}
                                        </div>
                                        <div class="col-span-4 text-sm">
                                            {{ displayedData[virtualRow.index].action }}
                                        </div>
                                    </div>

                                    <!-- Expanded content -->
                                    <div
                                        v-if="expanded[virtualRow.index]"
                                        class="px-4 pb-4 bg-gray-50 dark:bg-gray-900/50"
                                    >
                                        <h4 class="font-semibold mb-2">
                                            Audit Log Details
                                        </h4>
                                        <div class="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="font-medium">Date:</span>
                                                <span class="ml-2">{{ new Date(displayedData[virtualRow.index].date).toLocaleString() }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium">Actor:</span>
                                                <span class="ml-2">{{ displayedData[virtualRow.index].actor }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium">Action:</span>
                                                <span class="ml-2">{{ displayedData[virtualRow.index].action }}</span>
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded">{{ JSON.stringify(displayedData[virtualRow.index], null, 2) }}</pre>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
